import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setTableList } from '@app/action/tableList';
import { Table } from '@components/common';
import { getCrumb } from '@utils/utils';
import {
  Button,
  Divider,
  Col,
  Form,
  Icon,
  Input,
  message,
  Modal,
  Row,
  Select,
  Timeline,
} from 'antd';
import { useRouteMatch } from 'react-router-dom';
import { CommonObject } from '@app/types';
import { setMenuHook } from '@app/utils/utils';

interface FilterState {
  keyword: string;
  status: string; // 'all' | 'muted' | 'normal'
}

interface MuteRecord {
  id: string;
  account_id: string;
  nick_name: string;
  chao_id: string;
  status: 'muted' | 'normal';
  last_operate_time: string;
  operator_name: string;
}

interface MuteLogRecord {
  id: string;
  operate_type: 'mute' | 'unmute';
  operate_time: string;
  operator_name: string;
  reason?: string;
}

export default function GroupChatMuteList(props: any) {
  const dispatch = useDispatch();
  const match = useRouteMatch();

  // Redux state
  const tableList = useSelector((state: any) => state.tableList);
  const tableCache = useSelector((state: any) => state.tableCache);
  const { current, size, records = [] } = useSelector((state: any) => state.tableList);

  // Local state
  const [filter, setFilter] = useState<FilterState>({
    keyword: '',
    status: 'all',
  });

  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [isInitialized, setIsInitialized] = useState(false);

  // 搜索状态
  const [search, setSearch] = useState<{
    keyword: string;
  }>({
    keyword: '',
  });

  // 操作日志弹窗
  const [operateLog, setOperateLog] = useState<{
    visible: boolean;
    logs: MuteLogRecord[];
    userInfo: {
      nick_name: string;
      chao_id: string;
    };
  }>({
    visible: false,
    logs: [],
    userInfo: {
      nick_name: '',
      chao_id: '',
    },
  });

  // 初始化
  useEffect(() => {
    setMenuHook(dispatch, props);

    if (tableCache?.beforeRoute === match?.path && tableCache.records.length > 0) {
      getData({ current: tableCache.current, size: tableCache.size });
    } else {
      getData({ current: 1 });
    }

    setIsInitialized(true);
  }, []);

  // 获取数据
  const getData = (overlap: CommonObject = {}) => {
    const params = { ...getFilter(), ...overlap };
    console.log('获取群聊禁言列表数据', params);
    // ✅ API方法预留但不实现
    // dispatch(getTableList('getGroupChatMuteList', 'list', params));

    // 模拟数据 - 按账号去重，显示最后一次操作记录
    const mockData = {
      total: 5,
      current: 1,
      size: 10,
      allData: {},
      records: [
        {
          id: '1',
          account_id: '12345',
          nick_name: '张三',
          chao_id: '1234567',
          status: 'muted',
          last_operate_time: '2024-01-01 10:30:00',
          operator_name: '管理员A',
        },
        {
          id: '2',
          account_id: '12346',
          nick_name: '李四',
          chao_id: '1234568',
          status: 'normal',
          last_operate_time: '2024-01-01 09:15:00',
          operator_name: '管理员B',
        },
        {
          id: '3',
          account_id: '12347',
          nick_name: '王五',
          chao_id: '1234569',
          status: 'muted',
          last_operate_time: '2024-01-01 08:45:00',
          operator_name: '管理员A',
        },
        {
          id: '4',
          account_id: '12348',
          nick_name: '赵六',
          chao_id: '1234570',
          status: 'normal',
          last_operate_time: '2024-01-01 07:20:00',
          operator_name: '管理员C',
        },
        {
          id: '5',
          account_id: '12349',
          nick_name: '孙七',
          chao_id: '1234571',
          status: 'muted',
          last_operate_time: '2024-01-01 06:10:00',
          operator_name: '管理员A',
        },
      ],
    };

    dispatch(setTableList(mockData));
  };

  // 获取过滤条件
  const getFilter = () => {
    const { current, size } = tableList;
    const filters: CommonObject = { current, size };

    // 添加所有筛选字段
    Object.keys(filter).forEach((key) => {
      const value = filter[key as keyof typeof filter];
      if (value !== '' && value !== 'all' && value !== undefined) {
        filters[key] = value;
      }
    });

    return filters;
  };

  // 处理过滤条件变化
  const handleFilterChange = (key: string, value: any) => {
    setFilter({
      ...filter,
      [key]: value,
    });
  };

  // 监听 filter 变化
  useEffect(() => {
    if (isInitialized) {
      getData({ current: 1 });
    }
  }, [filter]);

  // 处理搜索
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // 处理搜索
  const handleSearch = () => {
    setFilter({
      ...filter,
      keyword: search.keyword,
    });
  };

  // ✅ 添加禁言功能 - 仅方法定义，不实现UI
  const addMuteUser = (accountId: string, reason?: string) => {
    console.log('添加禁言用户', { accountId, reason });
    // ✅ API调用预留
    // api.addMuteUser({ account_id: accountId, reason }).then(() => {
    //   message.success('禁言成功');
    //   getData();
    // });
  };

  // 处理添加禁用账号按钮点击
  const handleAddMuteUser = () => {
    console.log('打开添加禁用账号弹窗');
    // ✅ 这里可以打开添加禁用账号的弹窗或跳转到相应页面
    // 由于要求仅实现方法定义，不实现对应的UI组件，所以这里只是预留方法
    message.info('添加禁用账号功能待实现');
  };

  // 取消禁言
  const handleUnmute = (record: MuteRecord) => {
    Modal.confirm({
      title: '确定取消禁言吗？',
      content: `将取消用户"${record.nick_name}"的禁言状态`,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        console.log('取消禁言', record);
        // ✅ API调用预留
        // api.unmuteUser({ account_id: record.account_id }).then(() => {
        //   message.success('取消禁言成功');
        //   getData();
        // });
        message.success('取消禁言成功');
        getData();
      },
    });
  };

  // 获取操作日志
  const getOperateLog = (record: MuteRecord) => {
    console.log('获取操作日志，用户ID：', record.account_id);
    // ✅ API调用预留
    // api.getMuteOperateLog({ account_id: record.account_id }).then((r: any) => {
    //   setOperateLog({
    //     visible: true,
    //     logs: r.data.logs,
    //     userInfo: {
    //       nick_name: record.nick_name,
    //       chao_id: record.chao_id,
    //     },
    //   });
    // });

    // 模拟操作日志数据
    const mockLogs: MuteLogRecord[] = [
      {
        id: '1',
        operate_type: 'mute',
        operate_time: '2024-01-01 10:30:00',
        operator_name: '管理员A',
        reason: '发布不当言论',
      },
      {
        id: '2',
        operate_type: 'unmute',
        operate_time: '2023-12-30 15:20:00',
        operator_name: '管理员B',
      },
      {
        id: '3',
        operate_type: 'mute',
        operate_time: '2023-12-29 09:10:00',
        operator_name: '管理员A',
        reason: '恶意刷屏',
      },
    ];

    setOperateLog({
      visible: true,
      logs: mockLogs,
      userInfo: {
        nick_name: record.nick_name,
        chao_id: record.chao_id,
      },
    });
  };

  // 显示用户详情
  const showUserDetail = (record: MuteRecord) => {
    console.log('显示用户详情', record);
    // ✅ 这里可以调用用户详情弹窗或跳转到用户详情页面
    // 参考 rated.tsx 中的 showUserDetailModal 方法
  };

  // 处理批量选择
  const handleSelectChange = (selectedKeys: any[]) => {
    setSelectedRowKeys(selectedKeys);
  };

  // 获取序号
  const getSeq = (i: number) => (current - 1) * size + i + 1;

  // 获取列配置
  const columns = [
    {
      title: '序号',
      key: 'seq',
      render: (_: any, __: any, i: number) => <span>{getSeq(i)}</span>,
      width: 60,
    },
    {
      title: '账号昵称',
      dataIndex: 'nick_name',
      key: 'nick_name',
      width: 150,
      render: (text: string, record: MuteRecord) => (
        <a onClick={() => showUserDetail(record)}>{text}</a>
      ),
    },
    {
      title: '小潮号',
      dataIndex: 'chao_id',
      key: 'chao_id',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      render: (status: string) => (
        <span style={{ color: status === 'muted' ? '#f5222d' : '#52c41a' }}>
          {status === 'muted' ? '禁言' : '正常'}
        </span>
      ),
    },
    {
      title: '最后操作时间',
      dataIndex: 'last_operate_time',
      key: 'last_operate_time',
      width: 160,
    },
    {
      title: '操作',
      key: 'op',
      width: 150,
      fixed: 'right',
      render: (_: any, record: MuteRecord) => (
        <span>
          {record.status === 'muted' && (
            <>
              <a onClick={() => handleUnmute(record)}>取消禁言</a>
              <Divider type="vertical" />
            </>
          )}
          <a onClick={() => getOperateLog(record)}>查看记录</a>
        </span>
      ),
    },
  ];

  return (
    <>
      <Row className="layout-infobar">
        <Col span={12}>
          <Button
            type="primary"
            onClick={handleAddMuteUser}
            style={{ marginRight: 8 }}
          >
            <Icon type="plus-circle" /> 添加禁用账号
          </Button>
        </Col>
        <Col span={12} className="layout-breadcrumb">
          {getCrumb(props.breadCrumb)}
        </Col>
      </Row>

      <div className="component-content">
        <Row style={{ marginBottom: 16 }}>
          <Col span={16}>
            <Form layout="inline">
              <Form.Item>
                <Select
                  value={filter.status}
                  onChange={(value) => handleFilterChange('status', value)}
                  style={{ width: 120 }}
                  placeholder="全部状态"
                >
                  <Select.Option value="all">全部状态</Select.Option>
                  <Select.Option value="muted">禁言</Select.Option>
                  <Select.Option value="normal">正常</Select.Option>
                </Select>
              </Form.Item>
            </Form>
          </Col>
          <Col span={8} style={{ textAlign: 'right' }}>
            <div style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}>
              <Input
                value={search.keyword}
                onChange={(e) => setSearch({ ...search, keyword: e.target.value })}
                placeholder="请输入账号昵称或小潮号"
                style={{ width: 200, marginRight: 8 }}
                onKeyDown={handleKeyDown}
              />
              <Button type="primary" onClick={handleSearch}>
                <Icon type="search" /> 搜索
              </Button>
            </div>
          </Col>
        </Row>

        <Table
          func="getGroupChatMuteList"
          index="list"
          rowKey="id"
          filter={getFilter()}
          columns={columns}
          pagination={true}
          multi={false}
          selectedRowKeys={selectedRowKeys}
          onSelectChange={handleSelectChange}
          tableProps={{ scroll: { x: 800 } }}
        />

        {/* 操作日志弹窗 - 参考 rated.tsx 样式 */}
        <Modal
          visible={operateLog.visible}
          title={`操作日志 - ${operateLog.userInfo.nick_name}（${operateLog.userInfo.chao_id}）`}
          width={600}
          onCancel={() => setOperateLog({ ...operateLog, visible: false })}
          onOk={() => setOperateLog({ ...operateLog, visible: false })}
          destroyOnClose={true}
        >
          <div>
            <Timeline>
              {operateLog.logs.length > 0 ? (
                operateLog.logs.map((log) => (
                  <Timeline.Item key={log.id}>
                    <div>
                      <div style={{ fontWeight: 'bold', marginBottom: 4 }}>
                        {log.operate_type === 'mute' ? '添加禁言' : '取消禁言'}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        操作时间：{log.operate_time}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        操作人：{log.operator_name}
                      </div>
                      {log.reason && (
                        <div style={{ color: '#666', fontSize: '12px' }}>
                          原因：{log.reason}
                        </div>
                      )}
                    </div>
                  </Timeline.Item>
                ))
              ) : (
                <Timeline.Item>
                  <div style={{ color: '#999' }}>暂无操作记录</div>
                </Timeline.Item>
              )}
            </Timeline>
          </div>
        </Modal>
      </div>
    </>
  );
}
